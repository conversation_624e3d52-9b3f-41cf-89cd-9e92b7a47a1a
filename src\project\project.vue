<template>
  <div id="project">
    <div class="home-page">
      <div class="top-bar flr">
        <div class="top-menu">
          <img src="./assets/icons/bjysptlogo.png" alt="img">
          <ul ref="selectls" :key="uid" class="select-content">
            <li v-for="(route, index) in menus" :key="index">
              <div :key="route.path + index + '-'" class="item-wrapper">
                <el-dropdown v-if="route.children && route.name !== 'r_opinion_inter' && route.name !== 'sys_home'" placement="top" @command="menuClick">
                  <span class="el-dropdown-link menu-title" :class="firstLevelMenuId===route['menuId']?'is-active':''" @click="menuClick(route)">
                    <span>{{ route['menuName'] }}</span>
                    <el-badge v-if="route['count']" class="my-badge" :value="route['count']" :max="99" />
                  </span>
                  <el-dropdown-menu class="top-menu-dropdown-none" />
                  <el-dropdown-menu v-if="route.children && route.name !== 'r_opinion_inter' && route.name !== 'sys_home'" slot="dropdown" class="top-menu-dropdown">
                    <el-dropdown-item v-for="(secondRoute, secondIndex) in route.children" :key="secondIndex" :style="active['menuId']===secondRoute['menuId']?'color:#ffd04b':''" class="top-menu-item top-menu-pop-class" :command="{ sub: secondRoute, top: route }">
                      <span class="title menu-title drop-menu">{{ secondRoute.menuName }}<el-badge v-if="secondRoute['count']" class="my-badge" :value="secondRoute['count']" :max="99" /></span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <template v-else>
                  <span class="menu-title el-dropdown-link" :class="firstLevelMenuId===route['menuId']?'is-active':''" @click="menuClick(route)">{{ route['menuName'] }}</span>
                </template>
              </div>
            </li>
          </ul>
        </div>
        <div class="info flr">
          <div v-if="manuallyList.length != 0" class="fc flr el-dropdown-link" title="帮助材料">
            <el-dropdown trigger="click" class="manually" placement="top">
              <i class="el-icon-reading" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in manuallyList" :key="index" :command="index" @click.native="downloadClick(item)">
                  <span>{{ item.paramKey }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div v-else class="fc flr"><i class="el-icon-reading" /></div>
          <el-popover popper-class="message-popper" placement="bottom" width="350" trigger="click">
            <MessageList @getUnReadCount="(count) => unReadCount = count" />
            <div slot="reference" class="fc flr msg-pop"><i class="el-icon-bell" />
              <div v-if="unReadCount>0" class="un-read">{{ unReadCount }}</div>
            </div>
          </el-popover>
          <el-popover popper-class="message-popper" placement="bottom" width="350">
            <MessageList @getUnReadCount="(count) => unReadCount = count" />
            <div v-if="user" slot="reference" class="fc flr user msg-pop"><i class="el-icon-user" />{{ user['userName'] }}</div>
          </el-popover>
          <div class="fc flr logout" @click="outFun"><i class="el-icon-switch-button" /></div>
        </div>
      </div>
      <div class="home-content">
        <div class="default-theme">
          <div v-if="leftMenu && lActiveId && active['menuUrl'] !== '/IndexPage'" class="theme-left" :style="{ width: `${lSize}%` }">
            <div class="menu-left">
              <!-- <div class="left-coll cursor-pointer" @click="collFun">
                <el-divider>
                  <i :class="[isCollapse ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left']" class="color-white" />
                  <span v-if="!isCollapse" class="color-white icon-size-mini p-l-small">{{ active['menuName'] }}</span>
                </el-divider>
              </div> -->
              <el-menu ref="lActiveMenu" class="over-auto" :collapse="isCollapse" :default-active="lActiveId" background-color="#1d3e92" text-color="#fff" active-text-color="#ffd04b">
                <template v-for="(child, i) in leftMenu">
                  <template v-if="child && child['children'] && child['children'].length > 0">
                    <el-submenu :key="i" :index="child['menuId']">
                      <template slot="title">
                        <i :class="child['iconCls']" class="el-icon- iconfont" />
                        <span slot="title" class="menu-title">{{ child['menuName'] }} <el-badge v-if="child['count']" class="my-badge" :value="child['count']" :max="99" /></span>

                      </template>
                      <template v-for="(m, c) in child['children']">
                        <template v-if="m && m['children'] && m['children'].length > 0">
                          <el-submenu :key="'m' + c" :index="m['menuId']">
                            <template slot="title">
                              <i :class="m['iconCls']" class="el-icon- iconfont" />
                              <span slot="title" class="menu-title">{{ m['menuName'] }} <el-badge v-if="m['count']" class="my-badge" :value="m['count']" :max="99" /></span>
                            </template>
                            <template v-for="(x) in m['children']">
                              <el-menu-item :key="i+'-'+x" :index="c['menuId']" @click="leftMenuClick(c)">
                                <i class="el-icon-arrow-right icon-right-angle" />
                                <span slot="title" class="menu-title">{{ c['menuName'] }} <el-badge v-if="c['count']" class="my-badge" :value="c['count']" :max="99" /></span>

                              </el-menu-item>
                            </template>
                          </el-submenu>
                        </template>
                        <el-menu-item v-else :key="i+'-'+c" :index="m['menuId']" @click="leftMenuClick(m)">
                          <i :class="m['iconCls']" class="el-icon- iconfont" />
                          <span slot="title" class="menu-title">{{ m['menuName'] }}
                            <el-badge v-if="m['count']" class="my-badge" :value="m['count']" :max="99" />
                          </span>
                        </el-menu-item>
                      </template>
                    </el-submenu>
                  </template>
                  <template v-else>
                    <el-menu-item :key="i" :index="child['menuId']" @click="leftMenuClick(child)">
                      <i :class="child['iconCls']" class="el-icon- iconfont" />
                      <span slot="title" class="menu-title">{{ child['menuName'] }}
                        <el-badge v-if="child['count']" class="my-badge" :value="child['count']" :max="99" />
                      </span>
                    </el-menu-item>
                  </template>
                </template>
              </el-menu>
            </div>
          </div>
          <div class="theme-right" :style="{ width: `calc(100% - ${lSize}%)` }">
            <div class="home-main">
              <div v-if="siteLists && active['menuUrl'] !== '/IndexPage'" class="home-main-site flr">
                <div class="site p-l-small p-r-small"><i class="el-icon-location color-white" /></div>
                <el-breadcrumb separator="/">
                  <el-breadcrumb-item v-for="(s, i) in siteLists" :key="'site' + i">
                    <div class="site">{{ s['menuName'] }}</div>
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </div>
              <div class="home-main-content" :style="{ height: active['menuUrl'] !== '/IndexPage' ? 'calc(100% - 30px)' : '100%' }">
                <router-view />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <suspended-ball /> -->
    <div class="phone">
      <div class="tip-top">
        <div class="p-b-mini">微信扫一扫</div>
        <div class="img">
          <img :src="supportInfo.qrcode" alt="微信扫一扫">
        </div>
        <div class="p-t-mini">
          <p>技术支持电话</p>
          <p style="color: #3990e9">{{ supportInfo.tel }}</p>
        </div>
      </div>
      <div class="kf">
        <div class="img"><img src="./assets/icons/kefu.svg" alt="客服"></div>
      </div>
    </div>
    <BayWindow v-if="bayWindowData" :params="bayWindowData" />
  </div>
</template>

<script>
import { customParamTypeByManual } from '../console/api/syscustomize.js'
import { getUserAuth, logout, countPageMessage, checkFwbRole, getNoticeInfo } from './api/home'
import { customParamByKey } from '../console/api/syscustomize'
import { getUserRoleList } from './api/dictionary'
import { mapActions, mapState } from 'vuex'
import MessageList from '@/project/components/home/<USER>'// 消息列表
import BayWindow from './components/home/<USER>'
export default {
  name: 'Project',
  components: {
    MessageList,
    BayWindow
  },
  data() {
    return {
      uid: 'firstUid',
      supportInfo: {
        qrcode: null, // 技术支持二维码
        tel: null // 技术支持电话
      },
      showPopper: true,
      unReadCount: 0, // 未读消息
      activeId: '',
      menus: null,
      user: null,
      lSize: 12,
      firstLevelMenuId: sessionStorage.getItem('firstLevelMenuId'),
      isCollapse: false,
      activeMenu: [],
      manuallyList: [],
      active: {},
      bayWindowData: null,
      leftActive: null,
      lActiveId: null,
      siteLists: null,
      leftMenu: null,
      timer: null,
      menuTodoCount: {}
    }
  },
  computed: {
    ...mapState('indexPage', ['menuList', 'siteList', 'num', 'organId'])
  },
  watch: {
    '$route': {
      handler(val) {
        getNoticeInfo({ routeUrl: val.fullPath }).then((res) => {
          this.bayWindowData = res
        })
      },
      deep: true
    },
    siteLists(list) {
      if (list && list.length > 0) {
        this.callSiteList(list)
        this.activeMenu = list[list.length - 1]
        this.active = this.activeMenu
        this.firstLevelMenuId = list[0]['menuId'] // 顶部菜单 选中状态
        this.lActiveId = this.activeMenu['menuId'] // 左侧菜单 选中状态
        window.sessionStorage.setItem('menuId', this.lActiveId)
        this.leftMenu = list[1]['children']
      }
    },
    'siteList'(val) {
      this.siteLists = val
    },
    num: {
      handler(val) {
        function findMenuItemByName(itemName, items) {
          const results = []

          function searchItems(name, menuItems) {
            for (const item of menuItems) {
              if (item.menuName === name) {
                results.push(item)
              }
              if (item.children && item.children.length > 0) {
                searchItems(name, item.children)
              }
            }
          }

          searchItems(itemName, items)
          return results
        }

        const menuItem = findMenuItemByName('议题管理', this.menus)
        console.log(menuItem, '结果')
        this.leftMenuClick(menuItem[0])
      }
    },
    menuTodoCount: {
      handler(val) {
        this.setMenuToDoCount(val)
      },
      deep: true
    }
  },
  created() {
    this.getJSZC()
    this.customParamTypeByManual()
  },
  mounted() {
    getUserRoleList().then((res) => {
      window.sessionStorage.setItem('role', JSON.stringify(res))
    })
    this.init()
  },
  beforeRouteEnter(to, from, next) {
    console.log(343242)
    next()
  },
  destroyed() {
    clearInterval(this.timer)
  },
  methods: {
    ...mapActions('indexPage', ['callSiteList', 'callUserInfo', 'callMenuList', 'callCompleteTime', 'callCheckFwbRole']),
    init() {
      if (window.sessionStorage.activeId) {
        this.activeId = window.sessionStorage.activeId
        getUserAuth().then(res => {
          this.menus = res['menus']
          this.callUserInfo(res['user'])
          this.callMenuList(res['menus'])
          this.callCompleteTime(res['currentDate'])
          if (window.sessionStorage['menuId']) {
            this.menuSelect(window.sessionStorage['menuId'])
          } else if (this.activeId) {
            this.menuSelect(this.activeId)
          }
          this.user = res['user']
          // getMenuToDoCount().then(res => {
          //   this.menuTodoCount = res
          // })
          checkFwbRole().then(result => {
            this.callCheckFwbRole(result)
            if (result.hasFwbRole === 'false') {
              this.menuList.forEach((item, index) => {
                if (item.menuCode === 'fwbHome') {
                  this.menuList.splice(index, 1)
                }
              })
            }
          })
        })
      } else {
        getUserAuth().then(res => {
          this.menus = res['menus']
          if (res['menus'] && res['menus'].length !== 0) {
            this.callUserInfo(res['user'])
            this.callMenuList(res['menus'])
            this.callCompleteTime(res['currentDate'])
            this.firstLevelMenuId = this.menus[0].menuId
            window.sessionStorage.setItem('firstLevelMenuId', this.menus[0].menuId)
            this.recursionMenu(this.menus[0])
            window.sessionStorage.setItem('activeId', this.active['menuId'])
            this.activeId = window.sessionStorage.activeId
            if (window.sessionStorage['menuId']) {
              this.menuSelect(window.sessionStorage['menuId'])
            } else if (this.activeId) {
              this.menuSelect(this.activeId)
            }
          }
          this.user = res['user']
          // getMenuToDoCount().then(res => {
          //   this.menuTodoCount = res
          // })
          checkFwbRole().then(result => {
            this.callCheckFwbRole(result)
            if (result.hasFwbRole === 'false') {
              this.menuList.forEach((item, index) => {
                if (item.menuCode === 'fwbHome') {
                  this.menuList.splice(index, 1)
                }
              })
            }
          })
        })
      }
    },
    // 递归查找末级菜单
    recursionMenu(menus) {
      if (menus.children && menus.children.length > 0) {
        this.recursionMenu(menus.children[0])
      } else {
        this.active = menus
      }
    },
    async setMenuToDoCount(val) {
      this.menuListArr = []
      if (val) {
        console.log(this.menuListArr)
        this.treeIterator(this.menus, (node) => {
          if (node['menuCode'] && val[node['menuCode']]) {
            this.$set(node, 'count', val[node['menuCode']])
          }
        })
        this.treeIterator(this.menus, node1 => {
          if (node1.children && node1.children.length > 0) {
            let sum = 0
            node1.children.forEach(a => {
              sum += a['count'] || 0
            })
            node1['count'] = sum
          }
        })
        this.treeIterator(this.menus, node2 => {
          if (node2.children && node2.children.length > 0) {
            let sum = 0
            node2.children.forEach(a => {
              sum += a['count'] || 0
            })
            node2['count'] = sum
          }
        })
        this.treeIterator(this.menus, node3 => {
          if (node3.children && node3.children.length > 0) {
            let sum = 0
            node3.children.forEach(a => {
              sum += a['count'] || 0
            })
            node3['count'] = sum
          }
        })
        this.uid = this.$uid()
      }
    },
    treeIterator(tree, func) {
      // 递归循环 查找每一个节点
      tree.forEach(node => {
        func(node)
        node.children && this.treeIterator(node.children, func)
      })
    },
    // 调用材料列表接口
    customParamTypeByManual() {
      customParamTypeByManual().then((res) => {
        this.manuallyList = res
      })
    },
    // 下载建议材料
    downloadClick(item) {
      this.downLoadFile(item['docId'])
    },
    collFun() {
      this.isCollapse = !this.isCollapse
      this.lSize = this.isCollapse ? 3.5 : 12
    },
    menuClick(data) {
      if (data && data['target'] === 'blank' && data['menuUrl']) {
        if (data['menuCode'] === 'fwbHome') {
          window.open(data['menuUrl'] + this.organId, '_blank')
        } else {
          window.open(data['menuUrl'], '_blank')
        }
      } else if (data && data.sub && data.sub['target'] === 'blank' && data.sub['menuUrl']) {
        window.open(data.sub['menuUrl'], '_blank')
      } else if (data && data.sub) {
        let menu = this.$getMenuLeaf(data.sub.menuId, this.menus)
        menu = menu[menu.length - 1]
        this.firstLevelMenuId = data.top.menuId
        window.sessionStorage.setItem('firstLevelMenuId', data.top.menuId)
        this.active = menu
        this.changeLeftMenu(menu)
      } else if (data['target'] === 'frame' && data['menuUrl'] && data['menuUrl'] === '/IndexPage') {
        this.active = data
        this.firstLevelMenuId = data.menuId
        window.sessionStorage.menuId = data['menuId']
        window.sessionStorage.setItem('activeId', data.menuId)
        window.sessionStorage.setItem('firstLevelMenuId', this.firstLevelMenuId)
        this.$router.push({ path: '/IndexPage' })
      }
    },
    leftMenuClick(menu) {
      console.log(menu, menu['menuUrl'])
      if (menu['menuUrl']) {
        this.$router.push({ path: menu['menuUrl'] })
      }
      window.sessionStorage.menuId = menu['menuId']
      this.siteLists = this.$getMenuLeaf(menu['menuId'], this.menus)
      this.callSiteList(this.siteLists)
    },
    changeLeftMenu(menu) {
      if (menu['children'] && menu['children'].length > 0) {
        this.leftMenu = []
        this.leftActive = []
        const la = menu['children'][0] || {}
        if (la['children'] && la['children'].length > 0) {
          setTimeout(() => {
            this.leftMenu = menu['children']
            this.leftActive = la['children'][0] || {}
            this.lActiveId = this.leftActive['menuId']
            window.sessionStorage.setItem('menuId', this.lActiveId)
            this.siteLists = this.$getMenuLeaf(this.lActiveId, this.menus)
            this.callSiteList(this.siteLists)
            this.$router.push({ path: this.leftActive['menuUrl'] })
          })
        } else {
          setTimeout(() => {
            this.leftMenu = menu['children']
            this.leftActive = la
            this.lActiveId = la['menuId']

            window.sessionStorage.setItem('menuId', this.lActiveId)
            this.$router.push({ path: this.leftActive['menuUrl'] })
            this.siteLists = this.$getMenuLeaf(this.lActiveId, this.menus)
            this.callSiteList(this.siteLists)
          })
        }
      }
    },

    menuSelect(val) {
      const arr = this.$getMenuLeaf(val, this.menus)
      if (arr && arr.length > 0) {
        if (arr.length > 1) {
          this.active = arr[1]
          this.changePage(arr[1], arr, val)
        } else {
          if (arr[0]['children'] && arr[0]['children'].length > 0) {
            this.changePage(arr[0], arr, val)
          } else {
            this.active = arr[0]
            window.sessionStorage.menuId = this.active['menuId']
            this.firstLevelMenuId = this.active['menuId']
            this.$router.push({ path: this.active['menuUrl'] })
          }
        }
      }
    },
    changePage(active, arr, val) {
      this.lActiveId = null

      this.leftMenu = active['children']
      if (arr[arr.length - 1] && arr[arr.length - 1]['children']) {
        const child = arr[arr.length - 1]['children']
        if (child[0] && child[0]['children'] && child[0]['children'].length > 0) {
          this.leftActive = child[0]['children'][0]
          this.lActiveId = child[0]['children'][0]['menuId']
        } else {
          this.leftActive = child[0]
          this.lActiveId = child[0]['menuId']
        }
        this.siteLists = this.$getMenuLeaf(this.lActiveId, this.menus)
        this.callSiteList(this.siteLists)
        window.sessionStorage.menuId = this.lActiveId
        this.$router.push({ path: this.leftActive['menuUrl'] })
      } else {
        window.sessionStorage.menuId = val
        this.siteLists = arr
        this.lActiveId = val
        const i = this.siteLists.findIndex(s => s['menuId'] === val)
        this.$router.push({ path: this.siteLists[i]['menuUrl'] })
        this.callSiteList(this.siteLists)
      }
    },
    outFun() {
      this.$uqConfirm('系统即将退出，确定退出吗？', () => {
        logout({ isLocalLogin: window.sessionStorage.login }).then(r => {
          window.sessionStorage.removeItem('token')
          window.sessionStorage.removeItem('Expiration')
          window.sessionStorage.removeItem('login')
          window.sessionStorage.removeItem('menuId')
          window.sessionStorage.removeItem('menu')
          window.sessionStorage.clear()
          window.location.href = (process.env.NODE_ENV === 'development' ? this.baseUrl : './') + r['toUrl']
        })
      })
    },
    getUnReadCount() {
      countPageMessage({
        isPage: true,
        queryParam: {
          isRead: 0
        }
      }).then(res => {
        this.unReadCount = res.total
      })
    },
    // 获取技术支持
    getJSZC() {
      customParamByKey({
        key: '技术支持-微信二维码'
      }).then(res => {
        res && (this.supportInfo.qrcode = res.paramValue)
      })
      customParamByKey({
        key: '技术支持-电话'
      }).then(res => {
        res && (this.supportInfo.tel = res.paramValue)
      })
    }
  }
}
</script>
<style lang="scss">
.menu-title {
  position: relative;
  display: inline-block;
  .el-badge {
    padding-left: 5px;
  }
}
.drop-menu {
  .my-badge {
    position: absolute;
  }
}
.top-menu-pop-class:hover {
  background: linear-gradient(180deg, #0a296f, #234797, #0d2f7b) !important;
  .el-menu-item {
    &:hover {
      background-color: #153885 !important;
    }
  }
}
.top-bar {
  .el-menu.el-menu--horizontal {
    border-bottom: none;
  }
  .is-active {
    border: 0.5px solid transparent;
    background: linear-gradient(
      to bottom,
      #0a296f,
      #234797,
      #0d2f7b
    ) !important;
    border-image: linear-gradient(
        to bottom,
        rgba(255, 238, 179, 0),
        #ffeeb3,
        rgba(255, 238, 179, 0)
      )
      1 1 !important;
    box-shadow: 0 0 4.5px 0 rgba(0, 0, 0, 0.25) !important;
    font-weight: 500 !important;
    color: #ffeeb3 !important;
  }
  .is-opened {
    .el-submenu__title {
      background: linear-gradient(180deg, #0a296f, #234797, #0d2f7b) !important;
    }
  }
  .el-submenu__title {
    background: transparent !important;
    border-bottom-width: 0 !important;
  }
}
</style>
<style scoped lang="scss">
.default-theme {
  display: flex;
  flex-direction: row;
  .theme-left {
    flex-shrink: 0;
  }
  .theme-right {
    flex: 1;
  }
}
.phone {
  position: fixed;
  right: 10px;
  bottom: 5px;
  z-index: 10;
  width: 40px;
  color: #666666;
  transition: all 0.3s;
  .tip-top {
    display: none;
    padding: 10px;
    width: 150px;
    box-sizing: border-box;
    margin-bottom: 10px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 0 8px 0 rgb(0 0 0 / 15%);
    text-align: center;
    .img {
      width: 130px;
      height: 130px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  &:hover {
    width: 150px;
    .tip-top {
      display: block;
    }
    .kf {
      width: 150px;
      .img {
        &:after {
          display: block;
          opacity: 1;
        }
      }
    }
  }
  .kf {
    display: flex;
    width: 40px;
    border-radius: 40px;
    background: linear-gradient(271deg, #3990e9, #f2f8fe 0);
    box-shadow: 0 0 4px 0 rgb(119 120 122 / 30%), inset 0 0 4px 0 #89c3ff;
    transition: all 0.3s;
    .img {
      cursor: pointer;
      width: 40px;
      height: 40px;
      text-align: center;
      line-height: 45px;
      position: relative;
      &:after {
        position: absolute;
        content: "技术支持";
        color: #3990e9;
        left: 50px;
        top: -3px;
        font-size: 18px;
        display: none;
        opacity: 0;
        transition: all 1.5s;
        white-space: nowrap;
        word-break: keep-all;
      }
    }
  }
}
#project {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: #010638;
}
.info {
  color: #ffffff;
  // position: absolute;
  z-index: 10;
  right: 0;
  top: 0;
  height: 65px;
  background-color: #335ea5;
  .user {
    i {
      display: inline-block;
      border-radius: 50px;
      border: 1px solid #ffffff;
      font-size: 16px;
      padding: 2px;
      margin-right: 4px;
    }
  }
  .msg-pop {
    padding: 0 10px;
    margin: 0 2px;
    cursor: pointer;
    height: 100%;
    position: relative;
    i {
      font-size: 20px;
    }
  }
  .un-read {
    position: absolute;
    padding: 0 10px;
    border-radius: 10px;
    background: #f5222d;
    height: 18px;
    line-height: 18px;
    top: 8px;
    transform: translateX(50%);
  }
  > div {
    padding: 0 10px;
    margin: 0 2px;
    cursor: pointer;
    &.logout:hover {
      background-color: rgb(215, 43, 3);
      color: #ffffff;
    }
    > i {
      font-size: 20px;
    }
  }
}
.home-page {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #072461;
  :deep(.splitpanes__pane) {
    background-color: transparent !important;
  }

  :deep(.splitpanes__splitter) {
    display: none !important;
    border: none !important;
    background-color: rgba(73, 133, 225, 0.24) !important;
    &:after,
    &:before {
      background-color: #fff0b7 !important;
    }
  }
  .top-menu {
    position: relative;
    display: flex;
    width: calc(100% - 290px);
    > img {
      margin-right: 15px;
    }
    .ysbtblack {
      text-shadow: 1px 1px 5px #00074e;
      cursor: pointer;
    }
    .dropdown {
      position: absolute;
      height: 100%;
      z-index: 3;
      right: 15%;
      top: 0;
      .el-dropdown {
        color: #ffffff;
      }
    }
  }
  .home-content {
    height: calc(100% - 60px);
    .default-theme {
      height: 100%;
    }
    .menu-left {
      height: 100%;
      box-sizing: border-box;
      background: linear-gradient(to bottom, #1d3e92, #174a95);

      .left-coll {
        height: 40px;
        padding-top: 4px;
        .el-divider {
          background-color: #57b8d9;
          :deep(.el-divider__text) {
            background-color: #5585dd;
          }
        }
      }
      .icon-right-angle {
        font-size: 12px;
        color: #ffffff;
        font-weight: 100;
      }
      .is-active {
        i.el-icon- {
          color: #fff !important;
        }
        > .icon-right-angle {
          color: #fff;
        }
      }
      i.el-icon- {
        color: #ffffff !important;
      }
      .el-menu {
        height: calc(100% - 44px);
        border-right: none;
        background: linear-gradient(to bottom, #1d3e92, #174a95);
        .el-submenu{
          position: relative;
          &::before {
            z-index: 99;
            display: block;
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: 0;
            left: 0;
            content: "";
            background: linear-gradient(
              to left,
              rgba(20, 60, 140, 0.3),
              #596ce4,
              rgba(20, 60, 140, 0.7)
            );
          }
        }
        .el-menu-item {
          color: #fff !important;
          &::before {
            z-index: 99;
            display: block;
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: 0;
            left: 0;
            content: "";
            background: linear-gradient(
              to left,
              rgba(20, 60, 140, 0.3),
              #596ce4,
              rgba(20, 60, 140, 0.7)
            );
          }
          &.is-active {
            background-color: rgba(255, 255, 255, 0.2) !important;
            border-right: 7px solid #eebe12;
          }
        }
      }
    }
    .home-main {
      height: 100%;
      .home-main-content {
        height: calc(100% - 30px);
        box-sizing: border-box;
        position: relative;
        background: #ffffff;
      }
      .home-main-site {
        padding-top: 6px;
        box-sizing: border-box;
        .site {
          display: inline-block;
          height: 24px;
          line-height: 24px;
          color: #ffffff;
        }
      }
    }
  }
  .menus {
    flex: 1;
    height: 100%;
    .menu {
      height: 100%;
      color: #ffffff;
      padding: 0 30px;
      cursor: pointer;
      border: 0.5px solid transparent;
      &:hover {
        background: linear-gradient(to bottom, #0a296f, #234797, #0d2f7b);
      }
      &.active {
        background: linear-gradient(to bottom, #0a296f, #234797, #0d2f7b);
        border-image: linear-gradient(
            to bottom,
            rgba(255, 238, 179, 0),
            #ffeeb3,
            rgba(255, 238, 179, 0)
          )
          1 1;
        box-shadow: 0 0 4.5px 0 rgba(0, 0, 0, 0.25);
        font-weight: 500;
        color: #ffeeb3;
      }
    }
  }
  .top-bar {
    height: 60px;
    background: linear-gradient(to right, rgb(31, 64, 147), rgb(56, 101, 169));
    box-shadow: 0 5px 2px rgb(0 14 48 / 30%);
    position: relative;
    overflow: hidden;
    z-index: 9;
    display: flex;
    justify-content: space-between;
    img {
      height: 100%;
      width: auto;
    }
  }
}
.menu-left {
  .el-divider--horizontal {
    display: flex;
    align-items: center;
    justify-content: center;
    :deep(.is-center) {
      left: inherit;
      transform: inherit;
    }
    :deep(.el-divider__text) {
      padding: 0 10px;
    }
  }
  .el-submenu {
    .el-menu-item {
      background-color: #1a3780 !important;
    }
  }
  .el-menu {
    // background: url(./assets/images/slider.png) no-repeat center !important;
    background-size: 100% 100%;
  }
  .el-submenu__title {
    position: relative;
    color: #fff !important;
    &::after {
      display: block;
      position: absolute;
      width: 100%;
      height: 1.5px;
      bottom: 0;
      left: 0;
      content: "";
      background: linear-gradient(to left, #2839a8, #596cb4, #293aa9);
    }
    i {
      color: #fff !important;
    }
  }
}
.top-menu-dropdown-none {
  display: none !important;
}
.top-menu-dropdown {
  border: none;
  margin-top: 0px !important;
  background: linear-gradient(180deg, #0a296f, #234797, #0d2f7b) !important;
  :deep(.popper__arrow) {
    display: none;
  }
}
.top-menu-pop-class {
  color: #ffffff;
  padding: 8px 17px !important;
  min-width: 137px;
  text-align: center;
}
.select-content {
  display: flex;
  // flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  // margin-left: 150px;
  .el-dropdown-link {
    cursor: pointer;
    user-select: none;
    height: 60px;
    line-height: 60px;
    display: inline-block;
    padding: 0 30px;
    color: #fff;
    font-size: 16px;
    letter-spacing: 1px;
    font-weight: 500 !important;
    white-space: nowrap;
  }
  .el-dropdown-link:hover {
    background: linear-gradient(180deg, #0a296f, #234797, #0d2f7b) !important;
  }
}
.el-dropdown-link {
  padding: 0 10px;
  margin: 0 2px;
  cursor: pointer;
  height: 100%;
  i {
    font-size: 20px;
    color: #ffffff;
  }
}
</style>
